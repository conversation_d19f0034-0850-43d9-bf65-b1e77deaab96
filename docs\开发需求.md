### **项目需求：Windows硬件工具箱开发（PySide6 + PySide6-Fluent-Widgets）**

#### **1. 界面要求**
- **布局**：
  - 左侧导航栏，右侧内容区域
  - 支持全局自适应
  - 支持全局主题切换（浅色/深色/跟随系统模式）
- **现代化设计**：
  - **必须要求**：最大化使用PySide6-Fluent-Widgets组件，禁止多组件嵌套使用
  - 遵循Fluent Design2.0设计规范（参考文档：[Microsoft Fluent Design](https://learn.microsoft.com/zh-cn/windows/apps/design/)）。
  - PySide6-Fluent-Widgets组件库文档：https://github.com/zhiyiYo/PyQt-Fluent-Widgets/tree/PySide6
  - 除快捷工具需要根据配置文件分类字段进行分类以外的其他视图无需分类、搜索、收藏、刷新等功能。必须严格按需求开发，绝对禁止添加额外功能
  - 整个项目所有视图界面都不使用折叠相关的组件或控件
- **软件图标**：
  - 使用`assets\icon.ico`作为应用图标。
  - 在使用PySide6-Fluent-Widgets中FluentIcon提供的图标前，你必须执行`python -c "from qfluentwidgets import FluentIcon; print([i for i in dir(FluentIcon) if not i.startswith('_')])"`Python脚本来获取所有可用的FluentIcon图标，避免使用不存在的FluentIcon图标
  - 除导航栏以外需要使用图标，视图界面禁止使用任何图标
- **启动动画**：
  - 包含图标、软件名称、版本、版权信息。
  - 主要作用：预加载部分信息
  - 必须以管理员方式运行，若未以管理员模式启动，弹出对话框提醒用户。并在所有涉及系统级操作前再次检测权限。
- **架构设计要求**
  采用松耦合的 MVP（Model-View-Presenter）分层架构：
    - **Model 层（核心业务逻辑）**：
      - 位置：`core/` 目录
      - 职责：处理数据获取、业务逻辑、系统操作
      - 特点：不直接与 UI 交互，通过信号或返回值传递结果

    - **View 层（用户界面）**：
      - 位置：`ui/views/` 目录
      - 职责：界面展示、布局定义、用户输入接收
      - 技术栈：PySide6 + PySide6-Fluent-Widgets
      - 特点：纯 UI 逻辑，不包含业务逻辑

    - **Presenter 层（协调控制）**：
      - 位置：`presenters/` 目录
      - 职责：连接 Model 和 View，处理用户交互，管理状态
      - 特点：处理异步任务，协调 UI 更新和后台逻辑

#### **2. 项目结构要求**
```
Windows硬件工具箱/
├── main.py                      # 程序入口点
├── app.py                       # 应用程序主类
├── splash.py                    # 启动动画实现
├── admin_check.py               # 管理员权限验证
├── assets/                      # 静态资源文件
│   ├── icon.ico                # 应用主图标
│   ├── alipay.png              # 支付宝收款码
│   └── wechat_pay.png          # 微信收款码
├── config/                      # 配置文件模块
│   ├── powershell_commands.py  # PowerShell 命令配置
│   ├── registry_commands.py    # 注册表操作配置
│   ├── system_cleanup.py       # 系统清理任务配置
│   ├── appx_packages.py        # UWP 应用包配置
│   ├── onedrive_cleanup.py     # OneDrive 清理配置
│   └── quick_tools.py          # 快捷工具配置
├── core/                        # Model 层 - 核心业务逻辑
│   ├── __init__.py
│   ├── hardware_info.py        # 硬件信息获取服务
│   ├── system_optimizer.py     # 系统优化服务
│   ├── app_manager.py          # 应用管理服务
│   ├── oc_tools_scanner.py     # 超频工具扫描服务
│   ├── quick_tools_manager.py  # 快捷工具管理服务
│   └── updater.py              # 软件更新检查服务
├── ui/                          # View 层 - 用户界面
│   ├── __init__.py
│   ├── main_window.py          # 主窗口容器
│   ├── navigation.py           # 左侧导航栏
│   └── views/                  # 功能视图模块
│       ├── __init__.py
│       ├── hardware_view.py    # 硬件信息展示视图
│       ├── optimization_view.py # 系统优化清理视图
│       ├── preinstalled_view.py # 预装应用管理视图
│       ├── overclocking_view.py # 超频工具管理视图
│       ├── quick_tools_view.py # 快捷工具视图
│       └── settings_view.py    # 设置与关于视图
├── presenters/                  # Presenter 层 - 业务协调
│   ├── __init__.py
│   ├── hardware_presenter.py   # 硬件信息业务协调
│   ├── optimization_presenter.py # 系统优化业务协调
│   ├── preinstalled_presenter.py # 预装应用业务协调
│   ├── overclocking_presenter.py # 超频工具业务协调
│   ├── quick_tools_presenter.py # 快捷工具业务协调
│   └── settings_presenter.py   # 设置关于业务协调
├── utils/                       # 通用工具类
│   ├── __init__.py
│   ├── system_utils.py         # 系统操作工具
│   ├── icon_extractor.py       # 可执行文件图标提取
│   ├── process_utils.py        # 进程管理工具
│   └── dialog_utils.py         # 对话框封装工具
└── OCTools/                     # 超频工具存放目录（打包时排除）
```

#### **3. 左侧导航栏功能**
- **硬件信息**：
  - 获取电脑详细硬件配置信息（可参考`部分思路解析.md`中的硬件信息获取方案，但不是完全复制）。
  - 界面中仅显示该文档中列出的JSON数据。
  - **必须注意**：防止界面阻塞问题，硬件信息获取需要异步执行，在启动动画期间预加载或显示加载状态
  - **界面要求**：
    - 卡片式网格布局
    - 网格自适应：根据窗口大小自动调整布局列数
    - 硬件信息直观简洁，不允许使用折叠和滚动，平铺展示，需要复制全部硬件信息的按钮功能

- **优化清理**：
  - **必须注意**：防止界面阻塞问题，所有优化清理操作需要异步执行，显示执行进度和状态反馈
  - **全局控制要求**：
    - 实现界面级全选复选框
    - 全选功能：一键选择/取消所有选项卡中的所有选项
    - 执行功能：支持一键执行所有任务或仅执行选中任务
  - **执行后操作**：重启文件资源管理器 + 删除 `iconcache.db` 文件
  - **选项卡分类**：
    1. **PowerShell设置**：必须使用复选框，以便实现全局控制要求。
       - 设置执行策略为`Bypass`（配置文件：`powershell_commands.py`中的`EXECUTION_POLICY_COMMAND`）。
       - 解锁电源高级选项（配置文件：`powershell_commands.py`中的`UNLOCK_POWER_COMMAND`）。
    2. **注册表优化**（配置文件：`registry_commands.py`）：必须使用复选框，以便实现全局控制要求。
       - 父子复选框联动规则：如父选中则所有子选中，子全选父自动选中，部分子选中父为半选。
       - 例如：任务栏相关设置为父，任务栏精确到秒为子。
       - 若任务包含多条注册表命令，只要有一条成功即视为执行成功。
    3. **系统清理**（配置文件：`system_cleanup.py`）：必须使用复选框，以便实现全局控制要求。
       - 清理系统临时文件、临时目录、预读取文件、用户临时文件、IE缓存、Web缓存、系统日志、回收站、更新缓存、缩略图缓存、DNS缓存。
       - 运行系统磁盘清理。

- **预装应用**：
  - **必须注意**：防止界面阻塞问题，应用卸载操作需要异步执行，显示卸载进度和错误处理
  - **全局控制要求**：
    - 实现界面级全选复选框
    - 支持一键选择/取消所有选项卡中的所有选项
    - 支持一键执行所有任务或仅执行选中任务
  - **选项卡分类**（配置文件：`appx_packages.py`）：必须使用复选框，以便实现全局控制要求。
    1. Windows Xbox相关应用。
    2. Windows Store商店应用。
    3. Windows音视频编解码器应用。
    4. Windows系统预装应用。
    5. 卸载并清理OneDrive（配置文件：`onedrive_cleanup.py`）：必须使用复选框，以便实现全局控制要求。
      - 停止OneDrive进程。
      - 卸载OneDrive UWP/传统应用。
      - 清理OneDrive文件夹、注册表项。
      - 禁用OneDrive文件资源管理器集成。
      - 清理OneDrive启动项。

- **超频工具**：
  - **必须注意**：防止阻塞问题，可以使用新建子进程打开超频工具
  - 提供一键进入BIOS按钮（需用户二次确认）。
  - 扫描`OCTools`文件夹中的可执行程序，支持用户自行添加工具。
    - 检查`OCTools`文件夹是否存在，不存在则创建。
    - 以文件夹名称作为工具名称，可执行路径为实际工具路径。
    - 提取可执行文件图标（可参考`部分思路解析.md`中的图标获取方案，但不是完全复制，只参考实际需要用到的部分）。
    - 启动前检查可执行文件类型（GUI/控制台程序），以适合方式运行。
    - 优先使用与文件夹同名的可执行文件，否则查找其他可执行文件。
    - 所有工具均以管理员方式运行。

- **快捷工具**（配置文件：`quick_tools.py`）：
  - **界面设计**：
    - 按钮式卡片布局
    - 网格自适应：根据窗口大小自动调整布局列数
  - **必须注意**：防止阻塞问题，可以使用子进程打开快捷工具
  - 根据提供的配置文件分类。
  - 以下工具需用户二次确认：
    - 安全模式（一次性操作，重启后恢复为正常模式）。
    - 重新启动、关机、睡眠、锁定计算机。
  - 其他工具点击按钮小卡片即可启动。
  

- **设置关于**：
  - **必须注意**：防止网络阻塞问题，更新检查需要异步执行，设置合理超时时间
  - **居中布局**：应用图标（`assets/icon.ico`）、名称、版本号、版权信息。
  - **使用PySide6-Fluent-Widgets中的设置卡组件布局**：
    - **检查更新**功能。
    - **赞助作者**：
      - 点击弹出自定义的对话框（使用SegmentedWidget选项卡），显示支付宝（`alipay.png`）和微信（`wechat_pay.png`）二维码。
    - **抖音主页**：点击跳转作者抖音主页。
    - **官方Q群**：点击跳转QQ群。

#### **4. 右侧内容区域要求**
- 每个界面视图顶部必须包含视图界面标题

#### **5. 配置文件要求**
    - 配置文件必须只引用提供的配置文件，不允许修改配置文件内容。


# Windows硬件工具箱 - 各视图界面组件设计方案

## 🏁 软件启动动画 (splash.py)
- 使用PySide6-Fluent-Widgets库的splashscreen组件编写启动动画



## 🎯 主窗口容器 (main_window.py)

### PySide6-Fluent-Widgets 组件
- **FluentWindow**: 主窗口容器，支持主题切换
- **NavigationInterface**: 左侧导航栏
- **StackedWidget**: 右侧内容区域容器


### 基础布局
```python
from qfluentwidgets import FluentWindow, NavigationInterface, StackedWidget
from qfluentwidgets import FluentIcon as FIcon
```

---

## 🔧 硬件信息视图 (hardware_view.py)

### 页面结构
- **标题区域**: 显示页面标题和描述
- **硬件信息展示区域**: 平铺展示硬件配置
- **操作按钮区域**: 复制全部信息按钮

### PySide6-Fluent-Widgets 组件
```python
# 主要组件
from qfluentwidgets import (
    ScrollArea,           # 主滚动容器
    CardWidget,          # 硬件信息卡片容器
    HeaderCardWidget,    # 标题卡片
    SimpleCardWidget,    # 简单信息卡片
    PrimaryPushButton,   # 复制按钮
    CaptionLabel,        # 描述文本
    TitleLabel,          # 标题文本
    BodyLabel,           # 正文标签
    FluentIcon           # 图标
)

# 布局组件
from PySide6.QtWidgets import QVBoxLayout, QHBoxLayout, QGridLayout
```

### 界面布局建议
```
┌───────────────────────────────────────────────┐
│ ┌─ HeaderCardWidget (页面标题) ──────────────┐ │
│ │ 🖥️ 硬件信息                              │ │
│ │ 查看当前计算机的详细硬件配置信息           │ │
│ └─────────────────────────────────────────┘ │
│                                            │
│ ┌─ CardWidget (CPU信息) ───────────────────┐ │
│ │ 💻 处理器: Intel Core i7-12700K          │ │
│ │ 🏗️ 架构: x64                            │ │
│ │ ⚡ 主频: 3.60 GHz                       │ │
│ └─────────────────────────────────────────┘ │
│                                            │
│ ┌─ CardWidget (内存信息) ───────────────────┐ │
│ │ 🧠 内存总量: 32.0 GB                     │ │
│ │ 📊 可用内存: 24.5 GB                     │ │
│ └─────────────────────────────────────────┘ │
│                                            │
│ ┌─ PrimaryPushButton ──────────────────────┐ │
│ │ 📋 复制硬件信息                       │ │
│ └─────────────────────────────────────────┘ │
└──────────────────────────────────────────────┘
```

---

## 🧹 优化清理视图 (optimization_view.py)

### 页面结构
- **全局控制区域**: 全选复选框、一键执行按钮
- **选项卡区域**: PowerShell设置、注册表优化、系统清理
- **执行状态区域**: 显示执行进度和结果

### 界面布局建议
```
┌───────────────────────────────────────────────┐
│ ┌─ HeaderCardWidget (页面标题) ──────────────┐ │
│ │ 🧹 系统优化清理                           │ │
│ │ 优化系统设置，清理垃圾文件，提升性能        │ │
│ └─────────────────────────────────────────┘ │
│                                            │
│ ┌─ CardWidget (全局控制) ───────────────────┐ │
│ │ ☑️ 全选所有选项                           │ │
│ │ [🚀 一键执行] [▶️ 执行选中]               │ │
│ └─────────────────────────────────────────┘ │
│                                            │
│ ┌─ SegmentedWidget (选项卡) ─────────────────────────┐ │
│ │ PowerShell设置 | 注册表优化 | 系统清理    │ │
│ │ ┌─ 选项卡内容 ─────────────────────────┐ │ │
│ │ │ ☑️ 设置执行策略为Bypass              │ │ │
│ │ │ ☑️ 解锁电源高级选项                  │ │ │
│ │ └───────────────────────────────────┘ │ │
│ └─────────────────────────────────────────┘ │
│                                            │
│ ┌─ ProgressBar (执行进度) ──────────────────┐ │
│ │ ████████████████████ 100%                │ │
│ └─────────────────────────────────────────┘ │
└──────────────────────────────────────────────┘
```

---

## 📱 预装应用视图 (preinstalled_view.py)

### 页面结构
- **全局控制区域**: 全选复选框、批量卸载按钮
- **选项卡区域**: Xbox应用、Store应用、编解码器、系统应用、OneDrive
- **卸载状态区域**: 显示卸载进度和结果

### 界面布局建议
```
┌───────────────────────────────────────────────┐
│ ┌─ HeaderCardWidget (页面标题) ──────────────┐ │
│ │ 📱 预装应用管理                           │ │
│ │ 卸载Windows预装应用，释放存储空间          │ │
│ └─────────────────────────────────────────┘ │
│                                            │
│ ┌─ CardWidget (全局控制) ───────────────────┐ │
│ │ ☑️ 全选                      [▶️ 卸载选中]│ │
│ │                                         │ │
│ └─────────────────────────────────────────┘ │
│                                            │
│ ┌─ SegmentedWidget (选项卡) ─────────────────────────┐ │
│ │ Xbox应用 | Store应用 | 编解码器 | 系统应用 │ │
│ │ ┌─ 应用列表 ───────────────────────────┐ │ │
│ │ │ ☑️ 🎮 Xbox Game Bar                 │ │ │
│ │ │ ☑️ 🎯 Xbox Identity Provider        │ │ │
│ │ │ ☑️ 📺 Xbox Video                   │ │ │
│ │ └───────────────────────────────────┘ │ │
│ └─────────────────────────────────────────┘ │
│                                            │
│ ┌─ ProgressRing (卸载进度) ─────────────────┐ │
│ │ ⭕ 正在卸载应用... 3/10                   │ │
│ └─────────────────────────────────────────┘ │
└──────────────────────────────────────────────┘
```

---

## 🚀 超频工具视图 (overclocking_view.py)

### 页面结构
- **快捷操作区域**: 一键进入BIOS按钮
- **工具网格区域**: 扫描到的超频工具卡片
- **工具管理区域**: 刷新扫描、添加工具说明

### 界面布局建议
```
┌───────────────────────────────────────────────┐
│ ┌─ HeaderCardWidget (页面标题) ──────────────┐ │
│ │ 🚀 超频工具管理                           │ │
│ │ 管理和启动各种超频工具，优化硬件性能        │ │
│ └─────────────────────────────────────────┘ │
│                                            │
│ ┌─ CardWidget (快捷操作) ───────────────────┐ │
│ │ [⚡ 一键进入BIOS] [🔄 打开工具文件夹]        │ │
│ └─────────────────────────────────────────┘ │
│                                            │
│ ┌─ FlowLayout (工具网格) ───────────────────┐ │
│ │ ┌─ ElevatedCardWidget ┐ ┌─ ElevatedCardWidget ─┐ │ │
│ │ │ 🖼️ [工具图标]      │ │ 🖼️ [工具图标]  │ │ │
│ │ │ MSI Afterburner   │ │ CPU-Z         │ │ │
│ │ │ [▶️ 启动]         │ │ [▶️ 启动]     │ │ │
│ │ └─────────────────┘ └─────────────────┘ │ │
│ └─────────────────────────────────────────┘ │
└──────────────────────────────────────────────┘
```

---

## 🛠️ 快捷工具视图 (quick_tools_view.py)

### 页面结构
- **工具分类区域**: 按配置文件分类显示
- **工具卡片网格**: 每个工具一个卡片
- **确认对话框**: 危险操作需要二次确认

### 界面布局建议
```
┌───────────────────────────────────────────────┐
│ ┌─ HeaderCardWidget (页面标题) ──────────────┐ │
│ │ 🛠️ 快捷工具                              │ │
│ │ 快速访问常用系统工具和功能                 │ │
│ └─────────────────────────────────────────┘ │
│                                            │
│ ┌─ CardWidget (系统工具分类) ───────────────┐ │
│ │ 💻 系统工具                              │ │
│ │ ┌─ FlowLayout ──────────────────────────┐ │ │
│ │ │ [⚙️ 控制面板] [📊 任务管理器] [🔧 注册表] │ │ │
│ │ │ [💾 磁盘管理] [🖥️ 设备管理器] [🌐 网络]  │ │ │
│ │ └───────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────┘ │
│                                            │
│ ┌─ CardWidget (电源操作分类) ───────────────┐ │
│ │ ⚡ 电源操作                              │ │
│ │ ┌─ FlowLayout ──────────────────────────┐ │ │
│ │ │ [🔄 重启] [⏻ 关机] [😴 睡眠] [🔒 锁定]  │ │ │
│ │ │ [🛡️ 安全模式] [⚠️ 需要确认操作]        │ │ │
│ │ └───────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────┘ │
└──────────────────────────────────────────────┘
```

---

## ⚙️ 设置关于视图 (settings_view.py)

### 页面结构
- **应用信息区域**: 图标、名称、版本、版权
- **功能按钮区域**: 检查更新、赞助作者、社交链接
- **主题设置区域**: 明暗主题切换

### 界面布局建议
```
┌───────────────────────────────────────────────┐
│ ┌─ HeaderCardWidget (应用信息) ──────────────┐ │
│ │ 🖼️ [应用图标]                            │ │
│ │ Windows硬件工具箱                         │ │
│ │ 版本 V1.0.0                              │ │
│ │ © 2025 作者名称. 保留所有权利.            │ │
│ └─────────────────────────────────────────┘ │
│                                            │
│ ┌─ SettingCardGroup (应用设置) ─────────────┐ │
│ │ ┌─ SwitchSettingCard ──────────────────┐ │ │
│ │ │ 🌙 深色模式        [🔘] 开关按钮      │ │ │
│ │ └───────────────────────────────────────┘ │ │
│ │                                          │ │
│ │ ┌─ PushSettingCard ────────────────────┐ │ │
│ │ │ 🔄 检查更新        [检查更新] 按钮     │ │ │
│ │ └───────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────┘ │
│                                            │
│ ┌─ SettingCardGroup (支持作者) ─────────────┐ │
│ │ ┌─ PushSettingCard ────────────────────┐ │ │
│ │ │ 💝 赞助作者        [查看二维码] 按钮   │ │ │
│ │ └───────────────────────────────────────┘ │ │
│ │                                          │ │
│ │ ┌─ HyperlinkCard ──────────────────────┐ │ │
│ │ │ 📱 抖音主页        [访问主页] 链接     │ │ │
│ │ └───────────────────────────────────────┘ │ │
│ │                                          │ │
│ │ ┌─ HyperlinkCard ──────────────────────┐ │ │
│ │ │ 👥 官方Q群         [加入群聊] 链接     │ │ │
│ │ └───────────────────────────────────────┘ │ │
│ └─────────────────────────────────────────┘ │
└──────────────────────────────────────────────┘
```

---

## 🎨 左侧导航栏 (navigation.py)

### 导航项配置
```python
# 导航项配置
navigation_items = [
    ("硬件信息", "hardware", FIcon.COMPUTER, NavigationItemPosition.TOP),
    ("优化清理", "optimization", FIcon.BROOM, NavigationItemPosition.TOP),
    ("预装应用", "preinstalled", FIcon.APPLICATION, NavigationItemPosition.TOP),
    ("超频工具", "overclocking", FIcon.SPEED_HIGH, NavigationItemPosition.TOP),
    ("快捷工具", "quick_tools", FIcon.TOOLBOX, NavigationItemPosition.TOP),
    ("设置关于", "settings", FIcon.SETTING, NavigationItemPosition.BOTTOM),
]
```
