#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 快捷工具配置
# 此配置文件包含所有可在快捷工具视图中显示的工具

QUICK_TOOLS = [
    {
        "category": "系统管理工具",
        "tools": [
            {"name": "控制面板", "command": "control"},
            {"name": "程序和功能", "command": "appwiz.cpl"},
            {
                "name": "上帝模式",
                "command": "shell:::{ED7BA470-8E54-465E-825C-99712043E01C}",
            },
            {
                "name": "设备管理器",
                "command": "devmgmt.msc",
            },
            {"name": "磁盘管理", "command": "diskmgmt.msc"},
            {"name": "任务管理器", "command": "taskmgr"},
            {"name": "组策略编辑器", "command": "gpedit.msc"},
            {"name": "本地安全策略", "command": "secpol.msc"},
            {"name": "服务管理", "command": "services.msc"},
            {"name": "计算机管理", "command": "compmgmt.msc"},
            {
                "name": "注册表编辑器",
                "command": "regedit",
            },
            {
                "name": "系统配置",
                "command": "msconfig",
            },
        ],
    },
    {
        "category": "系统设置",
        "tools": [
            {
                "name": "Windows设置",
                "command": "start ms-settings:",
            },
            {
                "name": "网络设置",
                "command": "start ms-settings:network",
            },
            {
                "name": "蓝牙设置",
                "command": "start ms-settings:bluetooth",
            },
            {
                "name": "电源选项",
                "command": "powercfg.cpl",
            },
            {
                "name": "性能选项",
                "command": "SystemPropertiesPerformance",
            },
            {
                "name": "桌面图标设置",
                "command": "rundll32.exe shell32.dll,Control_RunDLL desk.cpl,,0",
            },
            {
                "name": "Windows防火墙",
                "command": "firewall.cpl",
            },
            {
                "name": "Internet选项",
                "command": "inetcpl.cpl",
            },
            {
                "name": "网络连接",
                "command": "ncpa.cpl",
            },
        ],
    },
    {
        "category": "诊断和监控",
        "tools": [
            {
                "name": "DirectX诊断工具",
                "command": "dxdiag",
            },
            {
                "name": "资源监视器",
                "command": "resmon",
            },
            {
                "name": "性能监视器",
                "command": "perfmon",
            },
            {
                "name": "系统信息",
                "command": "msinfo32",
            },
            {
                "name": "事件查看器",
                "command": "eventvwr.msc",
            },
            {
                "name": "证书管理",
                "command": "certmgr.msc",
            },
        ],
    },
    {
        "category": "系统维护",
        "tools": [
            {
                "name": "命令提示符",
                "command": "cmd",
            },
            {
                "name": "PowerShell",
                "command": "powershell",
            },
            {
                "name": "安全模式",
                "command": "bcdedit /set {current} safeboot minimal",
                "description": "重启计算机后将进入安全模式，完成后自动恢复正常启动",
            },
        ],
    },
    {
        "category": "电源控制",
        "tools": [
            {
                "name": "重启",
                "command": "shutdown /r /t 0",
            },
            {
                "name": "关机",
                "command": "shutdown /s /t 0",
            },
            {
                "name": "睡眠",
                "command": "rundll32.exe powrprof.dll,SetSuspendState 0,1,0",
            },
            {
                "name": "锁定计算机",
                "command": "rundll32.exe user32.dll,LockWorkStation",
            },
        ],
    },
]
